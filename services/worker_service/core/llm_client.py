"""
LLM客户端
支持多种LLM提供商，用于Worker中的非流式分析
"""

import httpx
from typing import Dict, Any, Optional
from shared.utils.logger import get_logger
from services.worker_service.config import config

logger = get_logger(__name__)


class LLMClient:
    """LLM客户端"""
    
    def __init__(self):
        self.provider = config.llm_provider
        self.http_client = httpx.AsyncClient(timeout=60.0)
        
        logger.info(f"LLM客户端初始化: 提供商={self.provider}")
    
    async def analyze(
        self, 
        prompt: str, 
        analysis_type: str = "default",
        streaming: bool = False
    ) -> str:
        """
        执行LLM分析
        
        Args:
            prompt: 分析提示词
            analysis_type: 分析类型
            streaming: 是否流式输出 (Worker中固定为False)
            
        Returns:
            分析结果文本
        """
        try:
            if self.provider == "deepseek":
                return await self._call_deepseek(prompt, analysis_type)
            elif self.provider == "openai":
                return await self._call_openai(prompt, analysis_type)
            else:
                raise ValueError(f"不支持的LLM提供商: {self.provider}")
                
        except Exception as e:
            logger.error(f"LLM分析失败: {e}")
            raise
    
    async def _call_deepseek(self, prompt: str, analysis_type: str) -> str:
        """调用DeepSeek API"""
        try:
            if not config.deepseek_api_key:
                raise ValueError("DeepSeek API密钥未配置")
            
            headers = {
                "Authorization": f"Bearer {config.deepseek_api_key}",
                "Content-Type": "application/json"
            }
            
            # 根据分析类型调整参数
            max_tokens = 2000
            temperature = 0.1
            
            if analysis_type == "quick":
                max_tokens = 1000
            elif analysis_type == "detailed":
                max_tokens = 4000
                temperature = 0.2
            
            data = {
                "model": config.deepseek_model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的风险分析师，擅长分析客户数据并评估违约风险。请基于提供的数据进行准确、客观的风险评估。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False  # Worker中不使用流式
            }
            
            logger.debug(f"调用DeepSeek API: 模型={config.deepseek_model}")
            
            response = await self.http_client.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            if response.status_code != 200:
                raise Exception(f"DeepSeek API调用失败: HTTP {response.status_code}, {response.text}")
            
            result = response.json()
            
            if "choices" not in result or not result["choices"]:
                raise Exception("DeepSeek API返回格式错误")
            
            content = result["choices"][0]["message"]["content"]
            logger.debug(f"DeepSeek API调用成功，返回内容长度: {len(content)}")
            
            return content
            
        except Exception as e:
            logger.error(f"DeepSeek API调用失败: {e}")
            raise
    
    async def _call_openai(self, prompt: str, analysis_type: str) -> str:
        """调用OpenAI API"""
        try:
            if not config.openai_api_key:
                raise ValueError("OpenAI API密钥未配置")
            
            headers = {
                "Authorization": f"Bearer {config.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            # 根据分析类型调整参数
            max_tokens = 2000
            temperature = 0.1
            
            if analysis_type == "quick":
                max_tokens = 1000
            elif analysis_type == "detailed":
                max_tokens = 4000
                temperature = 0.2
            
            data = {
                "model": config.openai_model,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的风险分析师，擅长分析客户数据并评估违约风险。请基于提供的数据进行准确、客观的风险评估。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False  # Worker中不使用流式
            }
            
            logger.debug(f"调用OpenAI API: 模型={config.openai_model}")
            
            response = await self.http_client.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            if response.status_code != 200:
                raise Exception(f"OpenAI API调用失败: HTTP {response.status_code}, {response.text}")
            
            result = response.json()
            
            if "choices" not in result or not result["choices"]:
                raise Exception("OpenAI API返回格式错误")
            
            content = result["choices"][0]["message"]["content"]
            logger.debug(f"OpenAI API调用成功，返回内容长度: {len(content)}")
            
            return content
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if self.provider == "deepseek":
                return await self._health_check_deepseek()
            elif self.provider == "openai":
                return await self._health_check_openai()
            else:
                return False
                
        except Exception as e:
            logger.error(f"LLM健康检查失败: {e}")
            return False
    
    async def _health_check_deepseek(self) -> bool:
        """DeepSeek健康检查"""
        try:
            if not config.deepseek_api_key:
                return False
            
            headers = {
                "Authorization": f"Bearer {config.deepseek_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": config.deepseek_model,
                "messages": [{"role": "user", "content": "test"}],
                "max_tokens": 1
            }
            
            response = await self.http_client.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            return response.status_code in [200, 400]  # 400也可能表示API可用但参数错误
            
        except Exception:
            return False
    
    async def _health_check_openai(self) -> bool:
        """OpenAI健康检查"""
        try:
            if not config.openai_api_key:
                return False
            
            headers = {
                "Authorization": f"Bearer {config.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": config.openai_model,
                "messages": [{"role": "user", "content": "test"}],
                "max_tokens": 1
            }
            
            response = await self.http_client.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data
            )
            
            return response.status_code in [200, 400]
            
        except Exception:
            return False
    
    async def cleanup(self):
        """清理资源"""
        await self.http_client.aclose()
