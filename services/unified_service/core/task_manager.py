"""
任务管理器
负责Celery任务的提交、状态查询和结果管理
"""

import json
from typing import Optional, Dict, Any
from celery.result import AsyncResult
from datetime import datetime

from shared.utils.logger import get_logger
from shared.models.file_info import FileInfo, FileStatus
from shared.celery_config import celery_app, TaskNames, TaskStatus
from services.unified_service.config import config

logger = get_logger(__name__)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.celery_app = celery_app
        logger.info("任务管理器初始化完成")
    
    async def submit_vectorization_task(
        self,
        file_code: str,
        file_path: str,
        file_info: FileInfo,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None,
        force_reprocess: bool = False
    ) -> AsyncResult:
        """
        提交向量化任务到Celery队列
        
        Args:
            file_code: 文件编码
            file_path: 文件路径
            file_info: 文件信息
            chunk_size: 分块大小
            chunk_overlap: 分块重叠
            force_reprocess: 是否强制重新处理
            
        Returns:
            Celery任务结果对象
        """
        try:
            # 准备任务参数
            task_kwargs = {
                "file_code": file_code,
                "file_path": file_path,
                "file_info": file_info.dict(),
                "chunk_size": chunk_size,
                "chunk_overlap": chunk_overlap,
                "force_reprocess": force_reprocess,
            }
            
            logger.info(f"提交向量化任务: {file_code}")
            
            # 提交任务到向量化队列
            task_result = self.celery_app.send_task(
                TaskNames.VECTORIZE_FILE,
                kwargs=task_kwargs,
                queue='vectorization',
                retry=True,
                retry_policy={
                    'max_retries': config.task_retry_max,
                    'interval_start': config.task_retry_delay,
                    'interval_step': 30,
                    'interval_max': 300,
                }
            )
            
            logger.info(f"向量化任务已提交: {file_code}, 任务ID: {task_result.id}")
            return task_result
            
        except Exception as e:
            logger.error(f"提交向量化任务失败 {file_code}: {e}")
            raise
    
    async def submit_analysis_task(
        self,
        file_code: str,
        analysis_type: str = "default",
        options: Optional[Dict[str, Any]] = None,
        priority: int = 1
    ) -> AsyncResult:
        """
        提交分析任务到Celery队列
        
        Args:
            file_code: 文件编码
            analysis_type: 分析类型
            options: 分析选项
            priority: 优先级
            
        Returns:
            Celery任务结果对象
        """
        try:
            # 准备任务参数
            task_kwargs = {
                "file_code": file_code,
                "analysis_type": analysis_type,
                "options": options or {},
                "priority": priority,
            }
            
            logger.info(f"提交分析任务: {file_code}")
            
            # 提交任务到默认队列
            task_result = self.celery_app.send_task(
                TaskNames.ANALYZE_RISK,
                kwargs=task_kwargs,
                queue='default',
                priority=priority,
                retry=True,
                retry_policy={
                    'max_retries': config.task_retry_max,
                    'interval_start': config.task_retry_delay,
                    'interval_step': 30,
                    'interval_max': 300,
                }
            )
            
            logger.info(f"分析任务已提交: {file_code}, 任务ID: {task_result.id}")
            return task_result
            
        except Exception as e:
            logger.error(f"提交分析任务失败 {file_code}: {e}")
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        try:
            task_result = AsyncResult(task_id, app=self.celery_app)
            
            status_info = {
                "task_id": task_id,
                "status": task_result.status,
                "result": None,
                "error": None,
                "progress": None,
                "created_at": None,
                "started_at": None,
                "completed_at": None,
            }
            
            # 根据任务状态获取详细信息
            if task_result.status == TaskStatus.SUCCESS:
                status_info["result"] = task_result.result
                status_info["completed_at"] = datetime.now()
                
            elif task_result.status == TaskStatus.FAILURE:
                status_info["error"] = str(task_result.info)
                status_info["completed_at"] = datetime.now()
                
            elif task_result.status == TaskStatus.STARTED:
                # 尝试获取进度信息
                if hasattr(task_result, 'info') and isinstance(task_result.info, dict):
                    status_info["progress"] = task_result.info
                status_info["started_at"] = datetime.now()
                
            elif task_result.status == TaskStatus.PENDING:
                status_info["created_at"] = datetime.now()
            
            return status_info
            
        except Exception as e:
            logger.error(f"获取任务状态失败 {task_id}: {e}")
            return {
                "task_id": task_id,
                "status": "ERROR",
                "error": str(e)
            }
    
    async def get_file_status(self, file_code: str) -> Optional[FileInfo]:
        """
        获取文件状态（通过查询相关任务）
        
        Args:
            file_code: 文件编码
            
        Returns:
            文件信息，如果不存在返回None
        """
        try:
            # 这里需要实现文件状态的持久化存储
            # 可以使用Redis或数据库来存储文件状态
            # 暂时返回None，具体实现在后续完善
            logger.debug(f"查询文件状态: {file_code}")
            return None
            
        except Exception as e:
            logger.error(f"获取文件状态失败 {file_code}: {e}")
            return None
    
    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        try:
            task_result = AsyncResult(task_id, app=self.celery_app)
            task_result.revoke(terminate=True)
            
            logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败 {task_id}: {e}")
            return False
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """
        获取队列统计信息
        
        Returns:
            队列统计信息
        """
        try:
            # 获取Celery检查信息
            inspect = self.celery_app.control.inspect()
            
            stats = {
                "active_tasks": inspect.active(),
                "scheduled_tasks": inspect.scheduled(),
                "reserved_tasks": inspect.reserved(),
                "registered_tasks": inspect.registered(),
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取队列统计失败: {e}")
            return {"error": str(e)}
    
    async def retry_failed_task(self, task_id: str) -> Optional[AsyncResult]:
        """
        重试失败的任务
        
        Args:
            task_id: 原任务ID
            
        Returns:
            新的任务结果对象，如果失败返回None
        """
        try:
            # 获取原任务信息
            original_task = AsyncResult(task_id, app=self.celery_app)
            
            if original_task.status != TaskStatus.FAILURE:
                logger.warning(f"任务 {task_id} 状态不是失败，无法重试")
                return None
            
            # 这里需要根据任务类型重新提交任务
            # 具体实现需要存储原始任务参数
            logger.info(f"重试任务: {task_id}")
            
            # 暂时返回None，具体实现在后续完善
            return None
            
        except Exception as e:
            logger.error(f"重试任务失败 {task_id}: {e}")
            return None
